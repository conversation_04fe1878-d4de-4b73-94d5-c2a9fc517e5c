import numpy as np
import torch
import time
from torch.utils.data import DataLoader
from dataset import ClientsSampler, ClientsDataset, TestDataset, data_partition, evaluate, evaluate_valid
from metric import NDCG_binary_at_k_batch, AUC_at_k_batch, HR_at_k_batch
import copy
import random
from untils import getModel


class Clients:
    """
    联邦学习客户端类 - 支持异构设备和多种优化算法
    主要功能:
    1. 支持不同设备类型（小型/中型/大型）及其对应嵌入维度
    2. 支持多种联邦优化算法 (base, UDL, DDR, RESKD)
    3. 支持本地梯度提取子矩阵
    4. 支持参数等价性维护
    """

    def __init__(self, config, logger):
        self.neg_num = config['neg_num']
        self.logger = logger
        self.config = config
        self.algorithm = config['algorithm']

        # 添加异构设备维度配置
        self.dim_s = config['dim_s']
        self.dim_m = config['dim_m']
        self.dim_l = config['dim_l']

        # 数据路径
        self.data_path = config['datapath'] + config['dataset'] + '/' + config['train_data']
        self.maxlen = config['max_seq_len']
        self.batch_size = config['batch_size']

        # 加载客户端数据集
        self.clients_data = ClientsDataset(self.data_path, self.neg_num, maxlen=self.maxlen)
        self.dataset = self.clients_data.get_dataset()
        self.user_train, self.user_valid, self.user_test, self.usernum, self.itemnum = self.dataset

        # 设备选择
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 初始化模型 - 使用大型设备维度作为基础
        self.model = getModel(config, self.clients_data.get_maxid())
        self.model.to(self.device)

        # 优化器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                          betas=(0.9, 0.98), weight_decay=config['l2_reg'])

        # 参数上传模式配置
        self.upload_mode = config['upload_mode']
        self.private_params = []
        self.non_private_params = []
        self._configure_param_upload_mode()

        # 为UDL系列算法加载特征子集长度配置
        if 'UDL' in self.algorithm:
            self.small_seq_len = config['small_seq_len']
            self.medium_seq_len = config['medium_seq_len']
            self.large_seq_len = config['large_seq_len']
            self.logger.info(
                f"特征子集长度配置: Ns={self.small_seq_len}, Nm={self.medium_seq_len}, Nl={self.large_seq_len}")

        # 记录客户端设备类型
        self.device_types = config.get('device_types', {})
        if not self.device_types:
            self._init_device_types()

        self.logger.info(f"异构设备配置: dim_s={self.dim_s}, dim_m={self.dim_m}, dim_l={self.dim_l}")
        self.logger.info(f"设备类型分布: "
                         f"小型:{sum(1 for t in self.device_types.values() if t == 's')}, "
                         f"中型:{sum(1 for t in self.device_types.values() if t == 'm')}, "
                         f"大型:{sum(1 for t in self.device_types.values() if t == 'l')}")

    def _init_device_types(self):
        """嵌套分配设备类型：小型(1/3) ⊆ 中型(2/3) ⊆ 大型(3/3)"""
        user_set = self.clients_data.get_user_set()
        total_users = len(user_set)
        user_list = list(user_set)
        np.random.shuffle(user_list)
        num_s = int(total_users * 1 / 3)
        num_m = int(total_users * 2 / 3)
        for i, uid in enumerate(user_list):
            if i < num_s:
                self.device_types[uid] = 's'
            elif i < num_m:
                self.device_types[uid] = 'm'
            else:
                self.device_types[uid] = 'l'
        self.logger.info("随机分配设备类型完成。")

    def get_dim_for_client(self, uid):
        """获取客户端的嵌入维度"""
        dev_type = self.device_types.get(uid, 'l')
        return {'s': self.dim_s, 'm': self.dim_m, 'l': self.dim_l}[dev_type]

    def _configure_param_upload_mode(self):
        """根据上传模式配置参数分类"""
        for name, param in self.model.named_parameters():
            if self.upload_mode == 'full':
                self.non_private_params.append(name)
            elif self.upload_mode == 'partial':
                if "embedding" in name:
                    self.non_private_params.append(name)
                else:
                    self.private_params.append(name)

    def load_server_model_params(self, model_param_state_dict):
        """从服务器加载模型参数，根据设备类型保留对应维度"""
        self.model.load_state_dict(model_param_state_dict, strict=False)

    def get_local_grads_for_device(self, uid, gradients):
        """根据设备类型提取梯度子矩阵"""
        dev_type = self.device_types.get(uid, 'l')
        device_grads = {}
        for name, grad in gradients.items():
            if grad is None:
                continue
            if 'item_embedding' in name or 'position_embedding' in name:
                if dev_type == 's':
                    device_grads[name] = grad[:, :self.dim_s].clone()
                elif dev_type == 'm':
                    device_grads[name] = grad[:, :self.dim_m].clone()
                else:  # 'l'
                    device_grads[name] = grad.clone()
            else:
                device_grads[name] = grad.clone()
        return device_grads

    def _compute_loss(self, seq_out, padding_mask, target_seq, neg_seq, input_len, dim, use_full_features=True):
        """(UDL专用)计算指定维度的损失，支持使用特征子集"""
        item_embedding_dim = self.model.item_embedding.weight[:, :dim].clone()
        if not use_full_features:
            if dim == self.dim_s:
                sub_seq_len = self.small_seq_len
            elif dim == self.dim_m:
                sub_seq_len = self.medium_seq_len
            else:
                sub_seq_len = self.large_seq_len
            sub_seq_len = min(input_len.item(), sub_seq_len)
            seq_out = seq_out[:, :sub_seq_len, :]
            padding_mask = padding_mask[:, :sub_seq_len]
            target_seq = target_seq[:, :sub_seq_len]
            neg_seq = neg_seq[:, :sub_seq_len, :]
        return self.model.loss_function(
            seq_out[:, :, :dim], padding_mask, target_seq, neg_seq, input_len, item_embedding=item_embedding_dim
        )

    def _compute_decorrelation_loss(self, embeddings):
        """(DDR专用)计算维度去相关正则化损失"""
        centered = embeddings - torch.mean(embeddings, dim=0, keepdim=True)
        std = torch.std(centered, dim=0, keepdim=True)
        std_safe = torch.where(std < 1e-8, torch.ones_like(std), std)
        normalized = centered / std_safe
        corr_matrix = torch.mm(normalized.T, normalized) / normalized.shape[0]
        loss = torch.norm(corr_matrix, p='fro') / embeddings.shape[1]
        return loss

    def train(self, uids, model_param_state_dict, epoch=0):
        """联邦学习客户端训练方法"""
        self.load_server_model_params(model_param_state_dict)
        clients_grads = {}
        clients_losses = {}
        self.model.train()

        for uid in uids:
            uid = uid.item()
            input_seq = self.clients_data.train_seq[uid]
            target_seq = self.clients_data.valid_seq[uid]
            input_len = self.clients_data.seq_len[uid]

            cand = np.setdiff1d(self.clients_data.item_set, self.clients_data.seq[uid])
            prob = self.clients_data.item_prob[cand]
            prob = prob / prob.sum()
            neg_seq = np.random.choice(cand, (input_len, self.neg_num), p=prob)
            neg_seq = np.pad(neg_seq, ((input_seq.shape[0] - input_len, 0), (0, 0)))

            input_seq = torch.from_numpy(input_seq).unsqueeze(0).to(self.device)
            target_seq = torch.from_numpy(target_seq).unsqueeze(0).to(self.device)
            neg_seq = torch.from_numpy(neg_seq).unsqueeze(0).to(self.device)
            input_len = torch.tensor(input_len).unsqueeze(0).to(self.device)

            seq_out = self.model(input_seq, input_len)
            padding_mask = (torch.not_equal(target_seq, 0)).float().to(self.device)

            loss = 0
            # --- 算法特定的损失计算 ---
            if self.algorithm == 'base':
                loss = self.model.baseModel_loss_function(seq_out, padding_mask, target_seq, neg_seq, input_len)
            elif self.algorithm == 'RESKD':
                dim = self.get_dim_for_client(uid)
                loss = self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, dim, use_full_features=True)
            elif 'UDL' in self.algorithm:
                dev_type = self.device_types.get(uid, 'l')
                if dev_type == 's':
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_s, use_full_features=True)
                elif dev_type == 'm':
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_s, use_full_features=False)
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_m, use_full_features=True)
                else:  # 'l'
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_s, use_full_features=False)
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_m, use_full_features=False)
                    loss += self._compute_loss(seq_out, padding_mask, target_seq, neg_seq, input_len, self.dim_l, use_full_features=True)

                if 'DDR' in self.algorithm:
                    alpha = self.config['decor_alpha']
                    if dev_type == 'm':
                        high_dim_embeddings = self.model.item_embedding.weight[:, self.dim_s:self.dim_m]
                        loss += alpha * self._compute_decorrelation_loss(high_dim_embeddings)
                    elif dev_type == 'l':
                        high_dim_embeddings = self.model.item_embedding.weight[:, self.dim_s:]
                        loss += alpha * self._compute_decorrelation_loss(high_dim_embeddings)
            else:
                raise ValueError(f"未知的联邦学习算法: {self.algorithm}")

            clients_losses[uid] = loss.item()
            self.optimizer.zero_grad()
            loss.backward()

            gradients = {name: param.grad.clone() if param.grad is not None else None for name, param in self.model.named_parameters()}
            clients_grads[uid] = self.get_local_grads_for_device(uid, gradients)
        return clients_grads, clients_losses


class Server:
    """
    联邦学习服务器类 - 支持多种优化算法
    """

    def __init__(self, config, clients, logger):
        self.clients = clients
        self.config = config
        self.algorithm = config['algorithm']
        self.batch_size = config['batch_size']
        self.epochs = config['epochs']
        self.early_stop = config['early_stop']
        self.maxlen = config['max_seq_len']
        self.skip_test_eval = config['skip_test_eval']
        self.eval_freq = config['eval_freq']
        self.early_stop_enabled = config['early_stop_enabled']
        self.use_heterogeneous_eval = config['use_heterogeneous_eval']
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.logger = logger
        self.dataset = self.clients.dataset

        self.dim_s = config['dim_s']
        self.dim_m = config['dim_m']
        self.dim_l = config['dim_l']

        self.model = getModel(config, self.clients.clients_data.get_maxid())
        self.model.to(self.device)

        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                          betas=(0.9, 0.98), weight_decay=config['l2_reg'])

        # --- 知识蒸馏相关初始化 ---
        self.use_distillation = config.get('use_distillation', False)
        if self.use_distillation and ('KD' in self.algorithm or 'RESKD' == self.algorithm):
            self.distill_lr = config['distill_lr']
            self.distill_epochs = config['distill_epochs']
            self.kd_num = config['kd_num']
            self.distill_optimizer = torch.optim.Adam(
                [self.model.item_embedding.weight, self.model.position_embedding.weight],
                lr=self.distill_lr
            )
            self.distill_losses = []
            logger.info("知识蒸馏已启用。")
        else:
            self.use_distillation = False

    def _padding(self, grad, target_dim):
        """梯度填充函数"""
        current_dim = grad.shape[1]
        if current_dim < target_dim:
            padding = torch.zeros((grad.shape[0], target_dim - current_dim), device=self.device)
            return torch.cat((grad, padding), dim=1)
        return grad

    def aggregate_gradients(self, clients_grads):
        """执行FedAvg聚合，支持异构维度填充"""
        clients_num = len(clients_grads)
        if clients_num == 0:
            return
        aggregated_gradients = {name: torch.zeros_like(param, device=self.device) for name, param in self.model.named_parameters() if param.requires_grad}

        for uid, grads_dict in clients_grads.items():
            for name, grad in grads_dict.items():
                if grad is None or name not in aggregated_gradients:
                    continue
                
                # 对嵌入层进行填充
                if 'embedding' in name and grad.shape[1] < aggregated_gradients[name].shape[1]:
                    padded_grad = self._padding(grad, aggregated_gradients[name].shape[1])
                    aggregated_gradients[name] += padded_grad
                else:
                    aggregated_gradients[name] += grad

        for name in aggregated_gradients:
            aggregated_gradients[name] /= clients_num

        for name, param in self.model.named_parameters():
            if name in aggregated_gradients:
                param.grad = aggregated_gradients[name]

    def relation_based_ensemble_distillation(self):
        """(RESKD/KD专用)基于关系的集成自知识蒸馏"""
        self.model.train()
        item_emb = self.model.item_embedding.weight
        kd_items = torch.randint(1, self.clients.itemnum + 1, (self.kd_num,)).to(self.device)
        
        for _ in range(self.distill_epochs):
            V_s = item_emb[kd_items, :self.dim_s]
            V_m = item_emb[kd_items, :self.dim_m]
            V_l = item_emb[kd_items, :]

            self.distill_optimizer.zero_grad()

            def cosine_distance_matrix(X):
                X_norm = X / (torch.norm(X, dim=1, keepdim=True) + 1e-8)
                return torch.mm(X_norm, X_norm.T)

            d_s = cosine_distance_matrix(V_s)
            d_m = cosine_distance_matrix(V_m)
            d_l = cosine_distance_matrix(V_l)

            d_ens = (d_s.detach() + d_m.detach() + d_l.detach()) / 3.0
            loss_s = torch.nn.functional.mse_loss(d_s, d_ens)
            loss_m = torch.nn.functional.mse_loss(d_m, d_ens)
            loss_l = torch.nn.functional.mse_loss(d_l, d_ens)
            total_loss = (loss_s + loss_m + loss_l) / 3
            total_loss.backward()
            self.distill_optimizer.step()
            self.distill_losses.append(total_loss.item())

    def _get_embedding_views(self):
        """获取当前模型不同维度的嵌入视图"""
        with torch.no_grad():
            item_emb = self.model.item_embedding.weight
            pos_emb = self.model.position_embedding.weight
            V_s = item_emb[:, :self.dim_s].clone()
            V_m = item_emb[:, :self.dim_m].clone()
            V_l = item_emb.clone()
            P_s = pos_emb[:, :self.dim_s].clone()
            P_m = pos_emb[:, :self.dim_m].clone()
            P_l = pos_emb.clone()
        return {'s': (V_s, P_s), 'm': (V_m, P_m), 'l': (V_l, P_l)}

    def evaluate_heterogeneous(self, dataset, maxlen, device='cuda'):
        """异构联邦学习评估方法 - 针对不同设备类型分别评估"""
        [train, valid, test, usernum, itemnum] = copy.deepcopy(dataset)
        device_users = {'s': [], 'm': [], 'l': []}
        for uid in range(1, usernum + 1):
            if uid in self.clients.device_types:
                device_users[self.clients.device_types[uid]].append(uid)
        
        results = {}
        embedding_views = self._get_embedding_views()

        for dev_type in ['s', 'm', 'l']:
            if not device_users[dev_type]:
                continue
            
            dim = self.config[f'dim_{dev_type}']
            temp_config = self.config.copy()
            temp_config['hidden_size'] = dim
            temp_model = getModel(temp_config, self.clients.clients_data.get_maxid())
            temp_model.to(device)
            
            with torch.no_grad():
                # 复制非嵌入参数
                for name, param in self.model.named_parameters():
                    if 'embedding' not in name and name in temp_model.state_dict():
                         if temp_model.state_dict()[name].shape == param.shape:
                            temp_model.state_dict()[name].copy_(param.data)
                # 复制对应维度的嵌入
                item_view, pos_view = embedding_views[dev_type]
                temp_model.item_embedding.weight.data.copy_(item_view)
                temp_model.position_embedding.weight.data.copy_(pos_view)

            ndcg, hr = self._evaluate_device_type(temp_model, dataset, maxlen, device, device_users[dev_type])
            results[dev_type] = {'ndcg': ndcg, 'hr': hr, 'user_count': len(device_users[dev_type])}
            del temp_model
        return results

    def _evaluate_device_type(self, model, dataset, maxlen, device, user_list):
        """评估特定设备类型的用户"""
        [train, valid, test, usernum, itemnum] = copy.deepcopy(dataset)
        NDCG, HT, valid_user = 0.0, 0.0, 0.0
        
        if len(user_list) > 10000:
            user_list = random.sample(user_list, 10000)
        
        for u in user_list:
            if len(train[u]) < 1 or len(test[u]) < 1: continue
            valid_user += 1
            seq = np.zeros([maxlen], dtype=np.int32)
            idx = maxlen - 1
            seq[idx] = valid[u][0]
            idx -= 1
            for i in reversed(train[u]):
                seq[idx] = i
                idx -= 1
                if idx == -1: break
            
            rated = set(train[u])
            rated.add(0)
            item_idx = [test[u][0]]
            for _ in range(100):
                t = np.random.randint(1, itemnum + 1)
                while t in rated: t = np.random.randint(1, itemnum + 1)
                item_idx.append(t)

            predictions = -model.predict(*[np.array(l) for l in [[u], [seq], item_idx]])
            rank = predictions[0].argsort().argsort()[0].item()

            if rank < 10:
                NDCG += 1 / np.log2(rank + 2)
                HT += 1
        return (NDCG / valid_user, HT / valid_user) if valid_user > 0 else (0.0, 0.0)

    def train(self):
        """联邦学习主训练循环"""
        user_set = self.clients.clients_data.get_user_set()
        uid_seq = [torch.tensor(user_set[i:i + self.batch_size]) for i in range(0, len(user_set), self.batch_size)]
        
        best_val_ndcg, no_improve_count = 0.0, 0
        
        for epoch in range(self.epochs):
            self.model.train()
            for uids in uid_seq:
                self.optimizer.zero_grad()
                clients_grads, _ = self.clients.train(uids, self.model.state_dict(), epoch)
                self.aggregate_gradients(clients_grads)
                self.optimizer.step()
                if self.use_distillation:
                    self.relation_based_ensemble_distillation()

            if (epoch + 1) % self.eval_freq == 0 or epoch == 0 or epoch == self.epochs - 1:
                self.model.eval()
                self.logger.info(f"--- Epoch {epoch+1} Evaluation ---")
                
                if self.use_heterogeneous_eval:
                    heterogeneous_results = self.evaluate_heterogeneous(self.dataset, self.maxlen, self.device)
                    total_users = sum(res['user_count'] for res in heterogeneous_results.values())
                    weighted_ndcg = sum(res['ndcg'] * res['user_count'] for res in heterogeneous_results.values()) / total_users if total_users > 0 else 0
                    weighted_hr = sum(res['hr'] * res['user_count'] for res in heterogeneous_results.values()) / total_users if total_users > 0 else 0
                    t_valid = (weighted_ndcg, weighted_hr)
                    for dev_type, result in heterogeneous_results.items():
                        self.logger.info(f"  设备类型 {dev_type}: NDCG@10={result['ndcg']:.4f}, HR@10={result['hr']:.4f}")
                    self.logger.info(f"  加权平均 Valid: NDCG@10={t_valid[0]:.4f}, HR@10={t_valid[1]:.4f}")
                else:
                    t_valid = evaluate_valid(self.model, self.dataset, self.maxlen, self.device)
                    self.logger.info(f"  Valid: NDCG@10={t_valid[0]:.4f}, HR@10={t_valid[1]:.4f}")

                if not self.skip_test_eval:
                    t_test = evaluate(self.model, self.dataset, self.maxlen, self.device)
                    self.logger.info(f"  Test: NDCG@10={t_test[0]:.4f}, HR@10={t_test[1]:.4f}")

                if self.early_stop_enabled:
                    if t_valid[0] > best_val_ndcg:
                        best_val_ndcg = t_valid[0]
                        no_improve_count = 0
                        self.logger.info (f"  新的最佳性能: NDCG@10={t_valid [0]:.4f}, HR@10={t_valid [1]:.4f}")
                        # 可以选择在这里保存最佳模型
                    else:
                        no_improve_count += 1
                    if no_improve_count >= self.early_stop:
                        self.logger.info(f"早停触发！NDCG在{self.early_stop}轮内没有改善。")
                        break
        self.logger.info("训练完成。")
