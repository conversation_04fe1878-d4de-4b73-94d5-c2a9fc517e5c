import numpy as np
import torch
import time  # 添加time模块用于计时
from torch.utils.data import DataLoader
from SASRec import SASRec
from dataset import ClientsSampler, ClientsDataset, TestDataset, data_partition, evaluate, evaluate_valid
from metric import NDCG_binary_at_k_batch, AUC_at_k_batch, HR_at_k_batch
import copy
import random


class Clients:
    """
    联邦学习客户端类 - 支持异构设备
    主要改进：
    1. 支持不同设备类型（小型/中型/大型）及其对应嵌入维度
    2. 支持本地梯度提取子矩阵
    3. 支持参数等价性维护
    """

    def __init__(self, config, logger):
        self.neg_num = config['neg_num']
        self.logger = logger
        self.config = config

        # 添加异构设备维度配置
        self.dim_s = config['dim_s']  # 小型设备嵌入维度
        self.dim_m = config['dim_m']  # 中型设备嵌入维度
        self.dim_l = config['dim_l']  # 大型设备嵌入维度

        # 数据路径
        self.data_path = config['datapath'] + config['dataset'] + '/' + config['train_data']
        self.maxlen = config['max_seq_len']
        self.batch_size = config['batch_size']

        # 加载客户端数据集
        self.clients_data = ClientsDataset(self.data_path, self.neg_num, maxlen=self.maxlen)
        self.dataset = self.clients_data.get_dataset()
        self.user_train, self.user_valid, self.user_test, self.usernum, self.itemnum = self.dataset

        # 设备选择
        self.device = "cuda" if torch.cuda.is_available() else "cpu"

        # 初始化SASRec模型 - 使用大型设备维度作为基础
        config['embed_size'] = self.dim_l  # 使用最大维度作为基础
        self.model = SASRec(config, self.clients_data.get_maxid())
        self.model.to(self.device)

        # 优化器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                          betas=(0.9, 0.98), weight_decay=config['l2_reg'])

        # 参数上传模式配置
        self.upload_mode = config['upload_mode']
        self.private_params = []
        self.non_private_params = []
        self._configure_param_upload_mode()

        # 添加特征子集长度配置
        self.small_seq_len = config['small_seq_len']  # Ns
        self.medium_seq_len = config['medium_seq_len']  # Nm
        self.large_seq_len = config['large_seq_len']  # Nl

        self.logger.info(
            f"特征子集长度配置: Ns={self.small_seq_len}, Nm={self.medium_seq_len}, Nl={self.large_seq_len}")

        # 记录客户端设备类型
        self.device_types = config.get('device_types', {})  # {uid: 's', 'm', 'l'}

        # 如果没有提供设备类型分配，使用随机分配
        if not self.device_types:
            self._init_device_types()

        self.logger.info(f"异构设备配置: dim_s={self.dim_s}, dim_m={self.dim_m}, dim_l={self.dim_l}")
        self.logger.info(f"设备类型分布: "
                         f"小型:{sum(1 for t in self.device_types.values() if t == 's')}, "
                         f"中型:{sum(1 for t in self.device_types.values() if t == 'm')}, "
                         f"大型:{sum(1 for t in self.device_types.values() if t == 'l')}")

    def _init_device_types(self):
        """嵌套分配设备类型：小型(1/3) ⊆ 中型(2/3) ⊆ 大型(3/3)"""
        user_set = self.clients_data.get_user_set()
        total_users = len(user_set)

        # 将用户列表转为可索引形式
        user_list = list(user_set)

        # 随机打乱用户顺序
        np.random.shuffle(user_list)

        # 计算各组的边界
        num_s = int(total_users * 1 / 3)  # 小型设备用户数
        num_m = int(total_users * 2 / 3)  # 中型设备用户数

        # 分配设备类型
        for i, uid in enumerate(user_list):
            if i < num_s:
                # 小型设备用户
                self.device_types[uid] = 's'
            elif i < num_m:
                # 中型设备用户
                self.device_types[uid] = 'm'
            else:
                # 大型设备用户
                self.device_types[uid] = 'l'

        # 统计各设备类型用户数（考虑嵌套关系）
        count_s = sum(1 for t in self.device_types.values() if t == 's')
        count_m = sum(1 for t in self.device_types.values() if t in ['s', 'm'])
        count_l = total_users  # 所有用户都是大型设备

        self.logger.info(f"嵌套设备类型分配完成: "
                         f"小型={count_s}({count_s / total_users:.1%}), "
                         f"中型={count_m}({count_m / total_users:.1%}), "
                         f"大型={count_l}({count_l / total_users:.1%})")

    def get_dim_for_client(self, uid):
        """获取客户端的嵌入维度"""
        dev_type = self.device_types.get(uid, 'l')
        return {
            's': self.dim_s,
            'm': self.dim_m,
            'l': self.dim_l
        }[dev_type]

    def random_neq(self, l, r, s):
        """
        生成不在集合s中的随机数，参考SAS.torch的random_neq函数

        参数:
        - l: 下界
        - r: 上界
        - s: 排除的集合

        返回:
        - t: 不在集合s中的随机数
        """
        t = np.random.randint(l, r)
        while t in s:
            t = np.random.randint(l, r)
        return t

    def dynamic_negative_sampling(self, user_id, input_len):
        """
        动态负采样函数，完全参考SAS.torch的采样逻辑
        为每个位置生成多个负样本，但采用SAS.torch的简单随机策略

        参数:
        - user_id: 用户ID
        - input_len: 输入序列长度

        返回:
        - neg_seq: 动态生成的负样本序列，形状为 (maxlen, neg_num)
        """
        # 获取用户交互过的物品集合
        user_items = set(self.clients_data.seq[user_id])

        # 获取输入序列和目标序列
        input_seq = self.clients_data.train_seq[user_id]
        target_seq = self.clients_data.valid_seq[user_id]
        maxlen = input_seq.shape[0]

        # 初始化负样本序列
        neg_seq = np.zeros((maxlen, self.neg_num), dtype=np.int32)

        # 为序列中每个有效位置生成负样本
        # 参考SAS.torch的逻辑：只为非填充位置生成负样本
        for pos in range(maxlen):
            if target_seq[pos] != 0:  # 只为非填充位置生成负样本
                for neg_idx in range(self.neg_num):
                    # 使用SAS.torch的简单随机策略：random_neq
                    neg_item = self.random_neq(1, self.clients_data.itemnum + 1, user_items)
                    neg_seq[pos, neg_idx] = neg_item

        return neg_seq

    def _configure_param_upload_mode(self):
        """根据上传模式配置参数分类"""
        for name, param in self.model.named_parameters():
            if self.upload_mode == 'full':
                # 全部上传模式：所有参数都上传到服务器，无隐私参数本地训练
                self.non_private_params.append(name)
            elif self.upload_mode == 'partial':
                # 部分上传模式：仅embedding参数上传，其他参数本地训练
                if "embedding" in name:
                    self.non_private_params.append(name)
                else:
                    self.private_params.append(name)

    def load_server_model_params(self, model_param_state_dict):
        """从服务器加载模型参数，根据设备类型保留对应维度"""
        current_state = self.model.state_dict()

        # 更新所有参数（但只保留设备对应维度的嵌入）
        for name, param in model_param_state_dict.items():
            if name in current_state:
                # 对于物品嵌入和位置嵌入，保留设备对应维度的子集
                if 'item_embedding' in name or 'position_embedding' in name:
                    dev_dim = self.get_dim_for_client(-1)  # -1作为特殊标记获取系统维度
                    if dev_dim < param.shape[1]:
                        # 只取前dev_dim列
                        param = param[:, :dev_dim]
                current_state[name] = param

        self.model.load_state_dict(current_state, strict=False)  # 允许维度变化

    def get_local_grads_for_device(self, uid, gradients):
        """
        根据设备类型提取梯度子矩阵
        考虑嵌套关系：
        - 小型设备：只提取前1/3维度
        - 中型设备：提取前2/3维度
        - 大型设备：提取全部维度
        """
        dev_type = self.device_types.get(uid, 'l')
        device_grads = {}

        for name, grad in gradients.items():
            if grad is None:
                continue

            # 对于物品嵌入，提取设备对应维度的子集
            if 'item_embedding' in name:
                if dev_type == 's':
                    # 小型设备：只取前1/3维度
                    device_grads[name] = grad[:, :self.dim_s].clone()
                elif dev_type == 'm':
                    # 中型设备：取前2/3维度
                    device_grads[name] = grad[:, :self.dim_m].clone()
                else:  # 'l'
                    # 大型设备：取全部维度
                    device_grads[name] = grad.clone()
            else:
                device_grads[name] = grad.clone()

        return device_grads

    def _compute_loss(self, seq_out, padding_mask, target_seq, neg_seq, input_len, dim, use_full_features=True):
        """
        计算指定维度的损失，支持使用特征子集
        """
        # 获取当前维度的物品嵌入子矩阵
        item_embedding_dim = self.model.item_embedding.weight[:, :dim].clone()

        # 根据是否使用完整特征决定输入序列
        if not use_full_features:
            # 根据设备类型确定特征子集长度
            if dim == self.dim_s:
                sub_seq_len = self.small_seq_len  # Ns
            elif dim == self.dim_m:
                sub_seq_len = self.medium_seq_len  # Nm
            else:
                sub_seq_len = self.large_seq_len  # Nl

            # 实际截取长度取配置值和实际序列长度的最小值
            sub_seq_len = min(input_len.item(), sub_seq_len)

            # 截取序列前部分
            seq_out = seq_out[:, :sub_seq_len, :]
            padding_mask = padding_mask[:, :sub_seq_len, :]
            target_seq = target_seq[:, :sub_seq_len]
            neg_seq = neg_seq[:, :sub_seq_len, :]
            input_len = torch.tensor(sub_seq_len).unsqueeze(0).to(self.device)

        return self.model.loss_function(
            seq_out[:, :, :dim],
            padding_mask,
            target_seq,
            neg_seq,
            input_len,
            item_embedding=item_embedding_dim  # 传入自定义嵌入矩阵
        )

    def train(self, uids, model_param_state_dict, epoch=0):
        """
        联邦学习训练方法 - 动态负采样版本
        实现标准的联邦学习流程：
        1. 每个客户端独立训练，保护数据隐私
        2. 动态生成负样本，提高学习效果
        3. 分离隐私梯度（不含embedding）和非隐私梯度（含embedding）
        4. 隐私梯度在本地更新，非隐私梯度上传服务器
        5. 返回所有客户端的非隐私梯度字典用于服务器聚合

        参数:
        - uids: 当前批次需要训练的客户端ID列表
        - model_param_state_dict: 从服务器接收的最新全局模型参数
        - epoch: 当前训练轮次，用于动态采样策略调整

        返回:
        - clients_grads: 所有客户端的非隐私梯度字典 {uid: {param_name: grad}}
        - clients_losses: 所有客户端的损失值字典 {uid: loss}
        """
        # 从服务器加载全局模型参数
        self.load_server_model_params(model_param_state_dict)

        # 存储客户端梯度和损失
        clients_grads = {}
        clients_losses = {}
        self.model.train()

        # 每个客户端独立训练
        for uid in uids:
            uid = uid.item()
            dev_type = self.device_types.get(uid, 'l')

            # 获取该用户的基础数据
            input_seq = self.clients_data.train_seq[uid]
            target_seq = self.clients_data.valid_seq[uid]
            input_len = self.clients_data.seq_len[uid]

            # 动态生成负样本（参考SAS.torch策略）
            if self.use_dynamic_sampling:
                neg_seq = self.dynamic_negative_sampling(uid, input_len)
            else:
                # 回退到原始的静态采样方式（如果需要）
                cand = np.setdiff1d(self.clients_data.item_set, self.clients_data.seq[uid])
                prob = self.clients_data.item_prob[cand]
                prob = prob / prob.sum()
                neg_seq = np.random.choice(cand, (input_len, self.neg_num), p=prob)
                neg_seq = np.pad(neg_seq, ((input_seq.shape[0] - input_len, 0), (0, 0)))

            # 数据格式转换并移动到指定设备
            input_seq = torch.from_numpy(input_seq).unsqueeze(0).to(self.device)
            target_seq = torch.from_numpy(target_seq).unsqueeze(0).to(self.device)
            neg_seq = torch.from_numpy(neg_seq).unsqueeze(0).to(self.device)
            input_len = torch.tensor(input_len).unsqueeze(0).to(self.device)

            # 处理序列长度限制
            max_seq_length = self.model.max_seq_length
            if input_seq.size(1) > max_seq_length:
                # 保留序列的后半部分（最近的交互）
                input_seq = input_seq[:, -max_seq_length:]
                target_seq = target_seq[:, -max_seq_length:]
                neg_seq = neg_seq[:, -max_seq_length:, :]
                input_len = torch.clamp(input_len, max=max_seq_length)

            # 单个客户端前向传播
            seq_out = self.model(input_seq, input_len)

            # 创建填充位置的掩码
            padding_mask = (torch.not_equal(input_seq, 0)).float().unsqueeze(-1).to(self.device)

            # 计算损失
            # 根据设备类型选择损失函数
            if dev_type == 's':
                # 小型设备：只计算小型模型损失（使用完整用户特征）
                loss = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_s, use_full_features=True
                )
            elif dev_type == 'm':
                # 中型设备：
                # 1. 计算小型模型部分损失（使用小型特征子集 Ns）
                loss_s = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_s, use_full_features=False  # 使用Ns长度
                )
                # 2. 计算中型模型整体损失（使用完整用户特征）
                loss_m = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_m, use_full_features=True
                )
                loss = loss_s + loss_m
            else:  # 'l'
                # 大型设备：
                # 1. 计算小型模型部分损失（使用小型特征子集 Ns）
                loss_s = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_s, use_full_features=False  # 使用Ns长度
                )
                # 2. 计算中型模型部分损失（使用中型特征子集 Nm）
                loss_m = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_m, use_full_features=False  # 使用Nm长度
                )
                # 3. 计算大型模型整体损失（使用完整用户特征）
                loss_l = self._compute_loss(
                    seq_out, padding_mask, target_seq, neg_seq, input_len,
                    self.dim_l, use_full_features=True
                )
                loss = loss_s + loss_m + loss_l

            # 保存损失值
            clients_losses[uid] = loss.item()

            # 反向传播计算梯度
            self.optimizer.zero_grad()
            loss.backward()

            # 获取当前梯度
            gradients = {}
            for name, param in self.model.named_parameters():
                gradients[name] = param.grad.clone() if param.grad is not None else None

            # 提取适合设备类型的梯度子集
            device_grads = self.get_local_grads_for_device(uid, gradients)

            # 分离隐私梯度和非隐私梯度
            non_private_grads = {}  # 非隐私梯度（含embedding的参数）

            if self.upload_mode == 'full':
                # 全部上传模式：收集所有梯度上传到服务器
                for name, param in self.model.named_parameters():
                    if param.grad is not None:
                        non_private_grads[name] = param.grad.clone()
                # 全部上传模式下不进行本地更新，等待服务器聚合后的参数
                self.optimizer.zero_grad()
            else:
                # 部分上传模式：仅收集embedding梯度上传，其他参数本地更新
                for name, param in self.model.named_parameters():
                    if param.grad is not None and name in self.non_private_params:
                        non_private_grads[name] = param.grad.clone()

                # 处理本地更新：清除上传参数的梯度，保留隐私参数的梯度
                for name, param in self.model.named_parameters():
                    if param.grad is not None and name in self.non_private_params:
                        param.grad = None  # 清除上传参数的梯度，不在本地更新

                # 本地更新隐私参数
                self.optimizer.step()

            # 存储该客户端的非隐私梯度
            clients_grads[uid] = non_private_grads
            clients_losses[uid] = loss.item()

        return clients_grads, clients_losses


class Server:
    """
    联邦学习服务器类
    主要功能：
    1. 实现FedAvg梯度聚合算法
    2. 处理多个客户端的梯度平均
    3. 维护全局模型并进行评估
    """

    def __init__(self, config, clients, logger):
        self.clients = clients
        self.config = config
        self.batch_size = config['batch_size']
        self.epochs = config['epochs']
        self.early_stop = config['early_stop']
        self.maxlen = config['max_seq_len']
        self.skip_test_eval = config['skip_test_eval']
        self.eval_freq = config['eval_freq']
        self.early_stop_enabled = config['early_stop_enabled']
        self.use_heterogeneous_eval = config['use_heterogeneous_eval']  # 是否使用异构评估
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.logger = logger
        self.dataset = self.clients.dataset

        # 获取异构设备维度配置
        self.dim_s = config['dim_s']
        self.dim_m = config['dim_m']
        self.dim_l = config['dim_l']

        # 初始化模型 - 使用大型设备维度
        config['embed_size'] = self.dim_l
        self.model = SASRec(config, self.clients.clients_data.get_maxid())
        self.model.to(self.device)

        # 初始化优化器
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['lr'],
                                          betas=(0.9, 0.98), weight_decay=config['l2_reg'])

        # 添加物品嵌入视图
        self.V_s = self.model.item_embedding.weight[:, :self.dim_s].detach().clone()
        self.V_m = self.model.item_embedding.weight[:, :self.dim_m].detach().clone()
        self.V_l = self.model.item_embedding.weight.detach().clone()

        # 添加位置嵌入视图
        self.P_s = self.model.position_embedding.weight[:, :self.dim_s].detach().clone()
        self.P_m = self.model.position_embedding.weight[:, :self.dim_m].detach().clone()
        self.P_l = self.model.position_embedding.weight.detach().clone()

        # 添加特征子集长度配置
        self.small_seq_len = config['small_seq_len']  # Ns
        self.medium_seq_len = config['medium_seq_len']  # Nm
        self.large_seq_len = config['large_seq_len']  # Nl

        self.logger.info(
            f"服务器特征子集长度配置: Ns={self.small_seq_len}, Nm={self.medium_seq_len}, Nl={self.large_seq_len}")

        self.logger.info(f"服务器嵌入初始化: V_s={self.V_s.shape}, V_m={self.V_m.shape}, V_l={self.V_l.shape}")
        self.logger.info(f"服务器位置嵌入初始化: P_s={self.P_s.shape}, P_m={self.P_m.shape}, P_l={self.P_l.shape}")

    def _padding(self, grad, target_dim):
        """梯度填充函数"""
        current_dim = grad.shape[1]
        if current_dim < target_dim:
            padding = torch.zeros((grad.shape[0], target_dim - current_dim), device=self.device)
            return torch.cat((grad, padding), dim=1)
        return grad

    def aggregate_gradients(self, clients_grads):
        """执行基于填充的梯度聚合 - 支持嵌套设备类型"""
        clients_num = len(clients_grads)
        if clients_num == 0:
            self.logger.warning("没有收到任何客户端梯度")
            return

        aggregated_gradients = {}

        # 初始化聚合梯度为零矩阵
        for name, param in self.model.named_parameters():
            if 'item_embedding' in name:
                # 物品嵌入使用最大维度
                aggregated_gradients[name] = torch.zeros(
                    (param.shape[0], self.dim_l), device=self.device
                )
            else:
                # 其他参数保持原维度
                aggregated_gradients[name] = torch.zeros_like(param, device=self.device)

        # 聚合每个客户端的梯度
        for uid, grads_dict in clients_grads.items():
            dev_type = self.clients.device_types.get(uid, 'l')

            for name, grad in grads_dict.items():
                if grad is None:
                    continue

                # 处理物品嵌入的填充
                if 'item_embedding' in name:
                    # 根据设备类型确定目标维度
                    if dev_type == 's':
                        # 小型设备：填充到最大维度
                        padded_grad = self._padding(grad, self.dim_l)
                    elif dev_type == 'm':
                        # 中型设备：填充到最大维度
                        padded_grad = self._padding(grad, self.dim_l)
                    else:  # 'l'
                        # 大型设备：不需要填充
                        padded_grad = grad

                    # 累加填充后的梯度
                    aggregated_gradients[name] += padded_grad
                else:
                    aggregated_gradients[name] += grad

        # 计算平均梯度
        for name in aggregated_gradients:
            aggregated_gradients[name] /= clients_num

        # 应用梯度到模型
        for name, param in self.model.named_parameters():
            if name in aggregated_gradients:
                # 对于物品嵌入，需要提取子矩阵
                if 'item_embedding' in name:
                    # 提取小型设备对应的部分
                    self.V_s_grad = aggregated_gradients[name][:, :self.dim_s].clone()

                    # 提取中型设备对应的部分
                    self.V_m_grad = aggregated_gradients[name][:, :self.dim_m].clone()

                    # 大型设备使用完整梯度
                    param.grad = aggregated_gradients[name]
                else:
                    param.grad = aggregated_gradients[name]
            else:
                param.grad = None

    def update_embeddings(self):
        """更新嵌入视图并确保参数等价性"""
        # 更新基准嵌入
        self.optimizer.step()

        # 更新所有设备视图
        base_weight = self.model.item_embedding.weight.data
        base_pos_weight = self.model.position_embedding.weight.data

        # 小型设备视图：前1/3维度
        self.V_s = base_weight[:, :self.dim_s].detach().clone()
        self.P_s = base_pos_weight[:, :self.dim_s].detach().clone()

        # 中型设备视图：前2/3维度
        self.V_m = base_weight[:, :self.dim_m].detach().clone()
        self.P_m = base_pos_weight[:, :self.dim_m].detach().clone()

        # 大型设备视图：全部维度
        self.V_l = base_weight.detach().clone()
        self.P_l = base_pos_weight.detach().clone()

        # 验证参数等价性
        if not torch.allclose(self.V_s, self.V_l[:, :self.dim_s], atol=1e-5):
            self.logger.warning("参数等价性警告: V_s != V_l[:dim_s]")
        if not torch.allclose(self.V_m, self.V_l[:, :self.dim_m], atol=1e-5):
            self.logger.warning("参数等价性警告: V_m != V_l[:dim_m]")
        if not torch.allclose(self.P_s, self.P_l[:, :self.dim_s], atol=1e-5):
            self.logger.warning("位置嵌入等价性警告: P_s != P_l[:dim_s]")
        if not torch.allclose(self.P_m, self.P_l[:, :self.dim_m], atol=1e-5):
            self.logger.warning("位置嵌入等价性警告: P_m != P_l[:dim_m]")

        # self.logger.info("嵌入视图更新并验证等价性")

    def evaluate ( self, data_loader ):
        """
        通用评估方法，用于在测试集或验证集上评估模型性能

        参数:
        - data_loader: 数据加载器 (测试集或验证集)

        返回:
        - ndcg10: NDCG@10指标
        - hr10: HR@10指标
        - auc: AUC指标
        - eval_time: 评估用时
        """
        # 记录评估开始时间
        start_time = time.time ()

        # 切换模型到评估模式
        self.model.eval ()

        # 初始化评估指标列表
        ndcg10_list, hr10_list, auc_list = [], [], []

        # 在评估过程中不计算梯度，节省内存和计算时间
        with torch.no_grad ():
            for input_seq, input_len, train_vec, target_vec in data_loader:
                # 将数据移动到指定设备
                input_seq = input_seq.to (self.device)
                input_len = input_len.to (self.device)

                # 处理序列长度限制
                max_seq_length = self.model.max_seq_length
                if input_seq.size (1) > max_seq_length:
                    # 保留序列后部分（最近交互）
                    input_seq = input_seq [:, -max_seq_length:]
                    input_len = torch.clamp (input_len, max = max_seq_length)

                # 获取模型预测
                pro = self.model (input_seq, input_len)
                recon_batch = []

                # 提取每个序列最后一个时间步的预测结果
                for i in range (input_seq.shape [0]):
                    # 对于前填充序列，计算最后一个有效位置
                    last_idx = min (input_seq.shape [1] - 1, input_len [i].item () - 1)
                    scores = pro [i, last_idx, :].unsqueeze (0)
                    recon_batch.append (scores)

                # 合并所有预测结果
                recon_batch = torch.cat (recon_batch)
                recon_batch = recon_batch.detach ().cpu ().numpy ()

                # 准备评估用的真实数据
                train_vec = train_vec.numpy ()
                target_vec = target_vec.numpy ()

                # 过滤掉用户已经交互过的物品
                # 将已交互物品的分数设为负无穷，确保不会被推荐
                recon_batch [train_vec.nonzero ()] = -np.inf

                # 计算各种评估指标
                n_10 = NDCG_binary_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)
                auc_b = AUC_at_k_batch (train_vec [:, 1:], recon_batch [:, 1:], target_vec [:, 1:])
                hr_10 = HR_at_k_batch (recon_batch [:, 1:], target_vec [:, 1:], 10)

                # 收集评估结果
                auc_list.append (auc_b)
                ndcg10_list.append (n_10)
                hr10_list.append (hr_10)

        # 计算各指标的平均值
        auc = np.mean (np.concatenate (auc_list))
        ndcg10 = np.mean (np.concatenate (ndcg10_list))
        hr10 = np.mean (hr10_list)

        # 计算总评估用时
        eval_time = time.time () - start_time

        return ndcg10, hr10, auc, eval_time

    def evaluate_heterogeneous(self, dataset, maxlen, device='cuda'):
        """
        异构联邦学习评估方法 - 针对不同设备类型分别评估
        
        参数:
        - dataset: 数据集分割结果
        - maxlen: 序列最大长度
        - device: 设备
        
        返回:
        - dict: 包含各设备类型的评估结果
        """
        [train, valid, test, usernum, itemnum] = copy.deepcopy(dataset)
        
        # 按设备类型分组用户
        device_users = {'s': [], 'm': [], 'l': []}
        for uid in range(1, usernum + 1):
            if uid in self.clients.device_types:
                dev_type = self.clients.device_types[uid]
                device_users[dev_type].append(uid)
        
        results = {}
        
        # 为每种设备类型进行评估
        for dev_type in ['s', 'm', 'l']:
            if not device_users[dev_type]:
                continue

            temp_config = self.config.copy()  # 复制全局配置作为基础

            # 获取该设备类型的嵌入维度
            if dev_type == 's':
                dim = self.dim_s
                embedding_view = self.V_s
                pos_embedding_view = self.P_s
                temp_config['small_seq_len'] = self.small_seq_len
            elif dev_type == 'm':
                dim = self.dim_m
                embedding_view = self.V_m
                pos_embedding_view = self.P_m
                temp_config['medium_seq_len'] = self.medium_seq_len
            else:  # 'l'
                dim = self.dim_l
                embedding_view = self.V_l
                pos_embedding_view = self.P_l
                temp_config['large_seq_len'] = self.large_seq_len
            

            temp_config['hidden_size'] = dim  # 确保隐藏层维度也匹配
            temp_model = SASRec(temp_config, self.clients.clients_data.get_maxid())
            temp_model.to(device)
            
            # 复制参数（只复制对应维度的嵌入）
            with torch.no_grad():
                # 复制物品嵌入（使用对应维度）
                temp_model.item_embedding.weight.data = embedding_view.clone()
                
                # 复制位置嵌入（使用对应维度）
                if hasattr(temp_model, 'position_embedding'):
                    temp_model.position_embedding.weight.data = pos_embedding_view.clone()
                
                # 复制其他参数（只复制维度匹配的参数）
                for name, param in self.model.named_parameters():
                    if 'item_embedding' not in name and 'position_embedding' not in name:
                        # 检查参数是否在临时模型中存在且维度匹配
                        if name in temp_model.state_dict():
                            temp_param = temp_model.state_dict()[name]
                            if temp_param.shape == param.shape:
                                temp_param.copy_(param.data)
                            else:
                                # 如果维度不匹配，跳过该参数（让模型使用默认初始化）
                                # 这是正常的，因为我们创建了不同维度的模型
                                pass
            
            # 评估该设备类型
            ndcg, hr = self._evaluate_device_type(temp_model, dataset, maxlen, device, device_users[dev_type])
            results[dev_type] = {'ndcg': ndcg, 'hr': hr, 'user_count': len(device_users[dev_type])}
            
            # 清理临时模型
            del temp_model
            
        return results
    
    def _evaluate_device_type(self, model, dataset, maxlen, device, user_list):
        """
        评估特定设备类型的用户
        
        参数:
        - model: 该设备类型对应的模型
        - dataset: 数据集
        - maxlen: 序列最大长度
        - device: 设备
        - user_list: 该设备类型的用户列表
        
        返回:
        - (NDCG@10, HR@10)
        """
        [train, valid, test, usernum, itemnum] = copy.deepcopy(dataset)
        
        NDCG = 0.0
        HT = 0.0
        valid_user = 0.0
        
        # 限制评估用户数量
        if len(user_list) > 10000:
            user_list = random.sample(user_list, 10000)
        
        for u in user_list:
            if len(train[u]) < 1 or len(test[u]) < 1:
                continue

            seq = np.zeros([maxlen], dtype=np.int32)
            idx = maxlen - 1
            seq[idx] = valid[u][0]
            idx -= 1
            for i in reversed(train[u]):
                seq[idx] = i
                idx -= 1
                if idx == -1:
                    break

            rated = set(train[u])
            rated.add(0)
            item_idx = [test[u][0]]
            for _ in range(100):
                t = np.random.randint(1, itemnum + 1)
                while t in rated:
                    t = np.random.randint(1, itemnum + 1)
                item_idx.append(t)

            predictions = -model.predict(*[np.array(l) for l in [[u], [seq], item_idx]])
            predictions = predictions[0]

            rank = predictions.argsort().argsort()[0].item()

            valid_user += 1

            if rank < 10:
                NDCG += 1 / np.log2(rank + 2)
                HT += 1
                
        if valid_user == 0:
            return 0.0, 0.0
            
        return NDCG / valid_user, HT / valid_user

    def train ( self ):
        """
        真正的联邦学习训练循环
        """
        # 创建客户端批次序列
        user_set = self.clients.clients_data.get_user_set()
        uid_seq = []
        for i in range(0, len(user_set), self.batch_size):
            batch_uids = user_set[i:i + self.batch_size]
            uid_seq.append(torch.tensor(batch_uids))

        # 早停相关变量
        best_val_ndcg, best_val_hr = 0.0, 0.0
        best_test_ndcg, best_test_hr = 0.0, 0.0

        # 早停计数器 - 记录连续没有NDCG改善的轮数
        no_improve_count = 0

        # 初始化模型参数
        for name, param in self.model.named_parameters():
            try:
                torch.nn.init.xavier_normal_(param.data)
            except:
                pass  # 忽略初始化失败的层

        # 设置embedding层的padding位置为0
        if hasattr(self.model, 'position_embedding'):
            self.model.position_embedding.weight.data[0, :] = 0
        if hasattr(self.model, 'item_embedding'):
            self.model.item_embedding.weight.data[0, :] = 0

        T = 0.0
        t0 = time.time()

        # 开始多轮训练
        early_stop_triggered = False  # 添加早停标志
        for epoch in range(self.epochs):
            # 记录epoch开始时间
            epoch_start_time = time.time()

            # 切换模型到训练模式
            self.model.train()

            # 训练统计变量
            batch_count = 0
            epoch_losses = []

            # 遍历所有客户端批次
            for uids in uid_seq:
                # 清零梯度
                self.optimizer.zero_grad()

                # 获取所有客户端的梯度字典和损失值字典
                clients_grads, clients_losses = self.clients.train(uids, self.model.state_dict(), epoch)

                # 收集本批次的损失值
                batch_losses = list(clients_losses.values())
                epoch_losses.extend(batch_losses)

                # 使用FedAvg梯度聚合方法
                self.aggregate_gradients(clients_grads)

                # 更新全局模型参数
                self.optimizer.step()

                # 记录批次统计
                batch_count += 1

            eval_freq = self.eval_freq
            # 在第一个epoch、每eval_freq个epoch或最后一个epoch进行评估
            should_evaluate = (epoch + 1) % eval_freq == 0 or epoch == 0 or epoch == self.epochs - 1
            if should_evaluate:
                self.model.eval()
                #t1是该轮训练时间 T为总时间
                t1 = time.time() - t0
                T += t1
                print('Evaluating', end='')

                # 选择评估方式
                if self.use_heterogeneous_eval:
                    # 异构联邦学习评估 - 针对不同设备类型分别评估
                    heterogeneous_results = self.evaluate_heterogeneous(self.dataset, self.maxlen, self.device)
                    
                    # 计算加权平均的验证集结果（用于早停）
                    total_users = 0
                    weighted_ndcg = 0.0
                    weighted_hr = 0.0
                    
                    for dev_type, result in heterogeneous_results.items():
                        user_count = result['user_count']
                        total_users += user_count
                        weighted_ndcg += result['ndcg'] * user_count
                        weighted_hr += result['hr'] * user_count
                    
                    if total_users > 0:
                        t_valid = (weighted_ndcg / total_users, weighted_hr / total_users)
                    else:
                        t_valid = (0.0, 0.0)

                    # 记录异构评估结果
                    self.logger.info(f"异构评估结果 - Epoch {epoch + 1}:")
                    for dev_type, result in heterogeneous_results.items():
                        self.logger.info(f"  设备类型 {dev_type}: NDCG@10={result['ndcg']:.4f}, HR@10={result['hr']:.4f}, 用户数={result['user_count']}")
                    self.logger.info(f"  加权平均: NDCG@10={t_valid[0]:.4f}, HR@10={t_valid[1]:.4f}")
                else:
                    # 传统评估方式
                    t_valid = evaluate_valid(self.model, self.dataset, self.maxlen, self.device)
                    self.logger.info(f"传统评估结果 - Epoch {epoch + 1}: NDCG@10={t_valid[0]:.4f}, HR@10={t_valid[1]:.4f}")

                # 检查评估结果是否异常
                if t_valid[0] > 1.0 or t_valid[1] > 1.0 or np.isnan(t_valid[0]) or np.isnan(t_valid[1]):
                    self.logger.warning(f"检测到异常评估结果: NDCG@10={t_valid[0]:.4f}, HR@10={t_valid[1]:.4f}")

                # 早停检查 - 检查NDCG是否有所改善
                if self.early_stop_enabled:
                    if t_valid[0] > best_val_ndcg:
                        # NDCG有改善，重置计数器
                        no_improve_count = 0
                        best_val_ndcg = t_valid[0]
                    else:
                        # NDCG没有改善，增加计数器
                        no_improve_count += 1

                    # 如果连续多轮没有改善，触发早停
                    if no_improve_count >= self.early_stop:
                        self.logger.info(f"早停触发！NDCG在{self.early_stop}轮内没有改善。")
                        early_stop_triggered = True

                # 测试集评估（可选）- 也使用异构评估
                if not self.skip_test_eval:
                    # 这里可以添加测试集的异构评估，暂时使用传统方法
                    t_test = evaluate(self.model, self.dataset, self.maxlen, self.device)

                    # 检查测试集评估结果是否异常
                    if t_test[0] > 1.0 or t_test[1] > 1.0 or np.isnan(t_test[0]) or np.isnan(t_test[1]):
                        self.logger.warning(f"检测到异常测试结果: NDCG@10={t_test[0]:.4f}, HR@10={t_test[1]:.4f}")

                    # 记录到日志
                    self.logger.info('epoch:%d, time: %f(s), valid (NDCG@10: %.4f, HR@10: %.4f), test (NDCG@10: %.4f, HR@10: %.4f) all_time: %f(s)'
                            % (epoch + 1, t1, t_valid[0], t_valid[1], t_test[0], t_test[1], T))
                else:
                    # 跳过测试集评估，设置默认值
                    t_test = (0.0, 0.0)
                    # 记录到日志
                    self.logger.info('epoch:%d, time: %f(s), valid (NDCG@10: %.4f, HR@10: %.4f), test: SKIPPED, all_time: %f(s)'
                            % (epoch + 1, t1, t_valid[0], t_valid[1],T))

                # 更新最佳结果
                if not self.skip_test_eval:
                    # 包含测试集评估的情况
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr or t_test[0] > best_test_ndcg or t_test[1] > best_test_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        best_test_ndcg = max(t_test[0], best_test_ndcg)
                        best_test_hr = max(t_test[1], best_test_hr)
                        self.logger.info(f"新的最佳性能: valid NDCG@10={best_val_ndcg:.4f}, test NDCG@10={best_test_ndcg:.4f}")

                else:
                    # 跳过测试集评估的情况，只基于验证集更新
                    if t_valid[0] > best_val_ndcg or t_valid[1] > best_val_hr:
                        best_val_ndcg = max(t_valid[0], best_val_ndcg)
                        best_val_hr = max(t_valid[1], best_val_hr)
                        self.logger.info(f"新的最佳性能: valid NDCG@10={best_val_ndcg:.4f}, valid HR@10={best_val_hr:.4f}")

                t0 = time.time()
                self.model.train()

                # 在评估完成后检查早停标志
                if early_stop_triggered:
                    break

            # 如果早停被触发，跳出训练循环
            if early_stop_triggered:
                break

        # 记录最佳结果
        if not self.skip_test_eval:
            self.logger.info('[联邦训练] 最佳结果: valid NDCG@10={:.4f}, HR@10={:.4f}, test NDCG@10={:.4f}, HR@10={:.4f}'.format(
                best_val_ndcg, best_val_hr, best_test_ndcg, best_test_hr))
        else:
            self.logger.info('[联邦训练] 最佳结果: valid NDCG@10={:.4f}, HR@10={:.4f} (测试集评估已跳过)'.format(
                best_val_ndcg, best_val_hr))